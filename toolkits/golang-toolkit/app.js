document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput');
    const contentSection = document.getElementById('section-content');
    const navItems = document.querySelectorAll('.nav-item');

    // Set light theme as default
    document.documentElement.setAttribute('data-theme', 'light');

    // Navigation
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const topic = item.getAttribute('data-topic');
            loadContent(topic);
            navItems.forEach(i => i.classList.remove('active'));
            item.classList.add('active');
        });
    });

    // Content loading
    async function loadContent(topic) {
        let url = '';
        let content = '';
        switch (topic) {
            case 'handbook':
                url = '../../golang-docs/go-comprehensive-handbook.md';
                content = await fetchAndParse(url, 'md');
                break;
            default:
                content = '<h1>Welcome to Golang Toolkit</h1><p>Select a topic from the sidebar to get started.</p>';
        }
        contentSection.innerHTML = content;
    }

    async function fetchAndParse(url, format) {
        try {
            // Show loading state
            contentSection.innerHTML = '<div class="loading">Loading content...</div>';
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to load ${url}: ${response.status} ${response.statusText}`);
            }
            
            const text = await response.text();
            
            if (format === 'md') {
                return parseMarkdown(text);
            }
            if (format === 'csv') {
                return parseCsv(text);
            }
            
            return text;
        } catch (e) {
            console.error('Error loading content:', e);
            return `
                <div class="error-message">
                    <h3>⚠️ Error Loading Content</h3>
                    <p><strong>File:</strong> ${url}</p>
                    <p><strong>Error:</strong> ${e.message}</p>
                    <p>Please check if the file exists and is accessible.</p>
                </div>
            `;
        }
    }

    function parseMarkdown(text) {
        // Enhanced markdown to HTML conversion
        let html = text;
        
        // Headers
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
        
        // Code blocks
        html = html.replace(/```go([\s\S]*?)```/gim, '<pre class="code-block go"><code>$1</code></pre>');
        html = html.replace(/```([\s\S]*?)```/gim, '<pre class="code-block"><code>$1</code></pre>');
        
        // Inline code
        html = html.replace(/`([^`]+)`/gim, '<code class="inline-code">$1</code>');
        
        // Bold and italic
        html = html.replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>');
        html = html.replace(/\*(.*?)\*/gim, '<em>$1</em>');
        
        // Links
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank">$1</a>');
        
        // Lists (simple)
        html = html.replace(/^- (.*$)/gim, '<li>$1</li>');
        
        // Convert to paragraphs
        return html.split('\n\n')
            .map(paragraph => {
                const trimmed = paragraph.trim();
                if (!trimmed) return '';
                if (trimmed.startsWith('<h') || trimmed.startsWith('<pre') || trimmed.startsWith('<ul') || trimmed.startsWith('<li')) {
                    return trimmed;
                }
                return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`;
            })
            .filter(p => p)
            .join('\n');
    }

    function parseCsv(text) {
        const rows = text.split('\n').filter(row => row.trim() !== '');
        if (rows.length === 0) return '<p>No data available.</p>';
        
        const headers = rows[0].split(',').map(h => h.trim());
        const data = rows.slice(1).map(row => row.split(',').map(cell => cell.trim()));

        let table = '<table class="csv-table"><thead><tr>';
        headers.forEach(header => table += `<th>${header}</th>`);
        table += '</tr></thead><tbody>';
        
        data.forEach(rowData => {
            table += '<tr>';
            rowData.forEach(cell => table += `<td>${cell}</td>`);
            table += '</tr>';
        });
        
        table += '</tbody></table>';
        return table;
    }

    // Add search functionality
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            // Simple content search within current content
            const content = contentSection.innerHTML;
            // This is a basic implementation - could be enhanced
        });
    }

    // Initial load
    loadContent('handbook');
    const firstNavItem = document.querySelector('.nav-item[data-topic="handbook"]');
    if (firstNavItem) {
        firstNavItem.classList.add('active');
    }
});